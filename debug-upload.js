const axios = require('axios');
const FormData = require('form-data');
const fs = require('fs');
const path = require('path');

// Create an axios instance with default config
const api = axios.create({
  baseURL: 'http://localhost:5000/api',
  headers: {
    'Content-Type': 'application/json',
  },
});

// Function to get a JWT token for authentication
async function getAuthToken() {
  try {
    const response = await axios.post('http://localhost:5000/api/auth/login', {
      username: 'admin',
      password: 'admin123'
    });
    return response.data.token;
  } catch (error) {
    console.error('Error getting auth token:', error.message);
    throw error;
  }
}

// Function to test file upload
async function testFileUpload() {
  try {
    // Get auth token
    const token = await getAuthToken();
    console.log('✅ Authentication successful');

    // Create a test image if it doesn't exist
    const testImagePath = path.join(__dirname, 'test-image.jpg');
    if (!fs.existsSync(testImagePath)) {
      // Create a simple 1x1 pixel image
      fs.writeFileSync(testImagePath, Buffer.from([
        0xff, 0xd8, 0xff, 0xe0, 0x00, 0x10, 0x4a, 0x46, 0x49, 0x46, 0x00, 0x01,
        0x01, 0x01, 0x00, 0x48, 0x00, 0x48, 0x00, 0x00, 0xff, 0xdb, 0x00, 0x43,
        0x00, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
        0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
        0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
        0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
        0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
        0xff, 0xff, 0xff, 0xff, 0xff, 0xc0, 0x00, 0x0b, 0x08, 0x00, 0x01, 0x00,
        0x01, 0x01, 0x01, 0x11, 0x00, 0xff, 0xc4, 0x00, 0x14, 0x00, 0x01, 0x00,
        0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
        0x00, 0x00, 0x00, 0xff, 0xc4, 0x00, 0x14, 0x10, 0x01, 0x00, 0x00, 0x00,
        0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
        0x00, 0xff, 0xda, 0x00, 0x08, 0x01, 0x01, 0x00, 0x00, 0x3f, 0x00, 0x37,
        0xff, 0xd9
      ]));
      console.log('Created test image file');
    }

    // Add token to API instance
    api.defaults.headers.common['Authorization'] = `Bearer ${token}`;

    // Create form data
    const formData = new FormData();
    formData.append('file', fs.createReadStream(testImagePath));
    formData.append('title', 'Test Image');
    formData.append('alt', 'Test Image Alt');

    // Test 1: Using axios directly with full URL
    console.log('\nTest 1: Using axios directly with full URL');
    try {
      const response1 = await axios.post('http://localhost:5000/api/media/upload', formData, {
        headers: {
          ...formData.getHeaders(),
          'Authorization': `Bearer ${token}`
        }
      });
      console.log('✅ Test 1 successful:', response1.data);
    } catch (error) {
      console.error('❌ Test 1 failed:', error.message);
      if (error.response) {
        console.error('Response status:', error.response.status);
        console.error('Response data:', error.response.data);
      }
    }

    // Test 2: Using api instance with relative URL
    console.log('\nTest 2: Using api instance with relative URL');
    try {
      const response2 = await api.post('/media/upload', formData, {
        headers: {
          ...formData.getHeaders()
        }
      });
      console.log('✅ Test 2 successful:', response2.data);
    } catch (error) {
      console.error('❌ Test 2 failed:', error.message);
      if (error.response) {
        console.error('Response status:', error.response.status);
        console.error('Response data:', error.response.data);
      }
    }

    // Test 3: Using axios with baseURL
    console.log('\nTest 3: Using axios with baseURL');
    try {
      const response3 = await axios.post('/media/upload', formData, {
        baseURL: 'http://localhost:5000/api',
        headers: {
          ...formData.getHeaders(),
          'Authorization': `Bearer ${token}`
        }
      });
      console.log('✅ Test 3 successful:', response3.data);
    } catch (error) {
      console.error('❌ Test 3 failed:', error.message);
      if (error.response) {
        console.error('Response status:', error.response.status);
        console.error('Response data:', error.response.data);
      }
    }

  } catch (error) {
    console.error('Test failed:', error.message);
  }
}

// Run the test
testFileUpload();
