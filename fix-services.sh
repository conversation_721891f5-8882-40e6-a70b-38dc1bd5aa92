#!/bin/bash
echo "Fixing API paths in service files..."

# List of service files to fix
SERVICE_FILES=(
  "src/services/experienceService.js"
  "src/services/offerService.js"
  "src/services/userService.js"
  "src/services/inquiryService.js"
  "src/services/mediaService.js"
  "src/services/settingsService.js"
  "src/services/authService.js"
)

# Function to fix a service file
fix_service_file() {
  local file=$1
  echo "Fixing $file..."
  
  # Add /api/ prefix to all API calls
  sed -i 's|api.get(\(['"'"'"]\)/|api.get(\1/api/|g' "$file"
  sed -i 's|api.post(\(['"'"'"]\)/|api.post(\1/api/|g' "$file"
  sed -i 's|api.put(\(['"'"'"]\)/|api.put(\1/api/|g' "$file"
  sed -i 's|api.delete(\(['"'"'"]\)/|api.delete(\1/api/|g' "$file"
  
  # Fix template literals
  sed -i 's|api.get(`/|api.get(`/api/|g' "$file"
  sed -i 's|api.post(`/|api.post(`/api/|g' "$file"
  sed -i 's|api.put(`/|api.put(`/api/|g' "$file"
  sed -i 's|api.delete(`/|api.delete(`/api/|g' "$file"
}

# Fix each service file
for file in "${SERVICE_FILES[@]}"; do
  if [ -f "$file" ]; then
    fix_service_file "$file"
  else
    echo "Warning: $file not found"
  fi
done

echo "All service files fixed!"
