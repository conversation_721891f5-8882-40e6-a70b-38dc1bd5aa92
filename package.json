{"name": "lux-voyage-travel-agency", "version": "0.1.0", "private": true, "dependencies": {"@fortawesome/fontawesome-svg-core": "^6.4.2", "@fortawesome/free-brands-svg-icons": "^6.4.2", "@fortawesome/free-regular-svg-icons": "^6.7.2", "@fortawesome/free-solid-svg-icons": "^6.4.2", "@fortawesome/react-fontawesome": "^0.2.0", "@testing-library/dom": "^9.3.1", "@testing-library/jest-dom": "^5.17.0", "@testing-library/react": "^13.4.0", "@testing-library/user-event": "^13.5.0", "axios": "^1.6.2", "bcryptjs": "^2.4.3", "body-parser": "^1.20.2", "compression": "^1.7.4", "cors": "^2.8.5", "dotenv": "^16.3.1", "express": "^4.18.2", "express-rate-limit": "^7.1.5", "express-sslify": "^1.2.0", "form-data": "^4.0.2", "helmet": "^7.1.0", "jsonwebtoken": "^9.0.2", "morgan": "^1.10.0", "multer": "^1.4.5-lts.1", "node-fetch": "^3.3.2", "pg": "^8.11.3", "pg-hstore": "^2.3.4", "react": "^18.2.0", "react-dom": "^18.2.0", "react-router-dom": "^6.20.0", "react-scripts": "5.0.1", "sequelize": "^6.35.2", "socket.io": "^4.8.1", "socket.io-client": "^4.8.1", "styled-components": "^6.1.1", "winston": "^3.11.0", "web-vitals": "^2.1.4"}, "scripts": {"start": "react-app-rewired start", "build": "react-app-rewired build", "test": "react-app-rewired test", "eject": "react-scripts eject", "server": "node server.js", "dev": "concurrently \"npm run server\" \"npm run start\"", "backend": "nodemon server.js", "frontend": "react-app-rewired start", "dev:watch": "concurrently \"npm run backend\" \"npm run frontend\"", "prod:start": "pm2 start ecosystem.config.js", "prod:stop": "pm2 stop ecosystem.config.js", "prod:restart": "pm2 restart ecosystem.config.js", "db:backup": "node scripts/backup.js", "db:migrate": "node scripts/migrate.js", "build:prod": "node scripts/build.js", "deploy:prod": "node scripts/deploy.js", "deploy:rollback": "node scripts/rollback.js", "analyze": "source-map-explorer 'build/static/js/*.js'", "lint": "eslint src/**/*.{js,jsx}", "lint:fix": "eslint src/**/*.{js,jsx} --fix", "format": "prettier --write \"src/**/*.{js,jsx,css,scss,json}\"", "validate": "npm run lint && npm run test", "test:env": "node scripts/test-env.js", "test:api": "node scripts/test-api-integration.js"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "devDependencies": {"concurrently": "^8.2.2", "nodemon": "^3.0.1", "pm2": "^5.3.1", "react-app-rewired": "^2.2.1", "serve": "^14.2.4"}}