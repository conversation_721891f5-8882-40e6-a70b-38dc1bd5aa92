{"name": "lux-voyage-travel-agency", "version": "0.1.0", "private": true, "dependencies": {"@fortawesome/fontawesome-svg-core": "^6.4.2", "@fortawesome/free-brands-svg-icons": "^6.4.2", "@fortawesome/free-regular-svg-icons": "^6.7.2", "@fortawesome/free-solid-svg-icons": "^6.4.2", "@fortawesome/react-fontawesome": "^0.2.0", "@testing-library/dom": "^9.3.1", "@testing-library/jest-dom": "^5.17.0", "@testing-library/react": "^13.4.0", "@testing-library/user-event": "^13.5.0", "axios": "^1.6.2", "body-parser": "^1.20.2", "cors": "^2.8.5", "express": "^4.18.2", "form-data": "^4.0.2", "jsonwebtoken": "^9.0.2", "multer": "^1.4.5-lts.1", "node-fetch": "^3.3.2", "react": "^18.2.0", "react-dom": "^18.2.0", "react-router-dom": "^6.20.0", "react-scripts": "5.0.1", "styled-components": "^6.1.1", "web-vitals": "^2.1.4"}, "scripts": {"start": "react-app-rewired start", "build": "react-app-rewired build", "test": "react-app-rewired test", "eject": "react-scripts eject", "server": "node server.js", "dev": "concurrently \"npm run server\" \"npm run start\"", "backend": "nodemon server.js", "frontend": "react-app-rewired start", "dev:watch": "concurrently \"npm run backend\" \"npm run frontend\""}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "devDependencies": {"concurrently": "^8.2.2", "nodemon": "^3.0.1", "react-app-rewired": "^2.2.1", "serve": "^14.2.4"}}