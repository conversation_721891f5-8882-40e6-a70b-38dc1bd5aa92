#!/bin/bash
echo "Starting Lux Voyage Travel Agency Full Application..."
echo ""

# Kill any existing processes on ports 3000 and 5000
echo "Checking for existing processes on ports 3000 and 5000..."
lsof -ti:3000,5000 | xargs kill -9 2>/dev/null

# Create necessary directories
echo "Setting up directories..."
mkdir -p uploads

# Create .env file if it doesn't exist
if [ ! -f ".env" ]; then
  echo "Creating .env file..."
  echo "REACT_APP_API_URL=http://localhost:5000/api" > .env
  echo "REACT_APP_SITE_NAME=Lux Voyage Travel Agency" >> .env
  echo "REACT_APP_UPLOAD_URL=http://localhost:5000/uploads" >> .env
fi

# Start the backend server in a separate terminal
echo "Starting backend server..."
gnome-terminal -- bash -c "cd $(pwd) && node server.js; read -p 'Press Enter to close...'"

# Wait for the backend to start
echo "Waiting for backend to start..."
sleep 3

# Check if backend is running
MAX_RETRIES=5
RETRY_COUNT=0
BACKEND_STARTED=false

while [ $RETRY_COUNT -lt $MAX_RETRIES ]; do
  if curl -s http://localhost:5000/api/destinations > /dev/null; then
    BACKEND_STARTED=true
    break
  fi
  echo "Backend not ready yet, retrying in 2 seconds..."
  sleep 2
  RETRY_COUNT=$((RETRY_COUNT + 1))
done

if [ "$BACKEND_STARTED" = false ]; then
  echo "Error: Backend failed to start after $MAX_RETRIES attempts."
  echo "Please check the backend terminal for error messages."
  exit 1
fi

echo "Backend started successfully!"
echo ""
echo "Starting frontend..."
echo ""
echo "The application will be available at:"
echo "  - Frontend: http://localhost:3000"
echo "  - Admin Dashboard: http://localhost:3000/admin/login"
echo "    Username: admin"
echo "    Password: admin123"
echo "  - Backend API: http://localhost:5000/api"
echo ""
echo "Press Ctrl+C to stop the frontend (backend will continue running in its own terminal)"
echo ""

# Start the frontend
npm start
