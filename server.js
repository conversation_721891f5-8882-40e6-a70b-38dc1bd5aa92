const express = require('express');
const cors = require('cors');
const bodyParser = require('body-parser');
const jwt = require('jsonwebtoken');
const multer = require('multer');
const path = require('path');
const fs = require('fs');

// Create Express app
const app = express();
const PORT = process.env.PORT || 5000;

// Middleware
app.use(cors());
app.use(bodyParser.json());
app.use(bodyParser.urlencoded({ extended: true }));

// JWT Secret
const JWT_SECRET = 'your-secret-key'; // In production, use environment variable

// JWT Configuration
const JWT_EXPIRES_IN = '7d'; // Extend token expiration to 7 days

// Mock database (in-memory storage)
const db = {
  users: [
    {
      id: 1,
      username: 'admin',
      password: 'admin123', // In production, use hashed passwords
      name: 'Admin User',
      email: '<EMAIL>',
      role: 'admin',
      createdAt: '2023-01-01'
    },
    {
      id: 2,
      username: 'editor',
      password: 'editor123',
      name: 'Editor User',
      email: '<EMAIL>',
      role: 'editor',
      createdAt: '2023-01-15'
    }
  ],
  destinations: [
    {
      id: 1,
      title: 'Luxury Resorts',
      slug: 'luxury-resorts',
      description: 'Experience the ultimate luxury in our handpicked resorts.',
      content: '<p>Our luxury resorts offer the finest accommodations, world-class dining, and exceptional service. Each resort is carefully selected to ensure an unforgettable experience.</p><p>From private beaches to infinity pools overlooking the ocean, these resorts provide the perfect setting for a dream vacation.</p>',
      imageUrl: '/uploads/destination-luxury-resorts.jpg',
      category: 'resorts',
      featured: true,
      createdAt: '2023-01-15'
    },
    {
      id: 2,
      title: 'Private Islands',
      slug: 'private-islands',
      description: 'Escape to your own private paradise on an exclusive island.',
      content: '<p>Experience the ultimate privacy and luxury on your own private island. These exclusive retreats offer unparalleled seclusion and personalized service.</p><p>With pristine beaches, crystal-clear waters, and dedicated staff, a private island vacation is the epitome of luxury travel.</p>',
      imageUrl: '/uploads/destination-private-islands.jpg',
      category: 'private-islands',
      featured: true,
      createdAt: '2023-02-10'
    },
    {
      id: 3,
      title: 'Boutique Hotels',
      slug: 'boutique-hotels',
      description: 'Discover unique and intimate boutique hotels with character.',
      content: '<p>Our carefully curated selection of boutique hotels offers intimate settings with distinctive character and personalized service.</p><p>Each property has its own unique charm, from historic buildings to contemporary designs, providing an authentic and memorable stay.</p>',
      imageUrl: '/uploads/destination-boutique-hotels.jpg',
      category: 'hotels',
      featured: false,
      createdAt: '2023-03-05'
    }
  ],
  experiences: [
    {
      id: 1,
      title: 'Sunset Dolphin Cruise',
      slug: 'sunset-dolphin-cruise',
      description: 'Witness playful dolphins against the backdrop of a stunning sunset.',
      content: '<p>Embark on a magical evening cruise to witness pods of dolphins playing in their natural habitat as the sun sets over the horizon.</p><p>Our experienced guides will take you to the best spots to observe these magnificent creatures while you enjoy refreshments on board.</p>',
      imageUrl: '/uploads/experience-dolphin-cruise.jpg',
      category: 'water-activities',
      duration: '2 hours',
      price: 120,
      featured: true,
      createdAt: '2023-01-20'
    },
    {
      id: 2,
      title: 'Traditional Maldivian Cooking Class',
      slug: 'maldivian-cooking-class',
      description: 'Learn to prepare authentic Maldivian dishes with local chefs.',
      content: '<p>Discover the secrets of Maldivian cuisine in this hands-on cooking class led by experienced local chefs.</p><p>You\'ll learn to prepare traditional dishes using fresh local ingredients and spices, followed by a delicious meal to enjoy your creations.</p>',
      imageUrl: '/uploads/experience-cooking-class.jpg',
      category: 'cultural',
      duration: '3 hours',
      price: 95,
      featured: true,
      createdAt: '2023-02-15'
    },
    {
      id: 3,
      title: 'Underwater Photography Tour',
      slug: 'underwater-photography-tour',
      description: 'Capture the vibrant marine life with professional guidance.',
      content: '<p>Join our professional underwater photographers for a guided snorkeling or diving tour focused on capturing the perfect shots of the vibrant coral reefs and marine life.</p><p>Suitable for all skill levels, this tour includes equipment rental and photography tips.</p>',
      imageUrl: '/uploads/experience-underwater-photography.jpg',
      category: 'water-activities',
      duration: '4 hours',
      price: 180,
      featured: false,
      createdAt: '2023-03-10'
    }
  ],
  offers: [
    {
      id: 1,
      title: 'Early Bird Summer Special',
      slug: 'early-bird-summer',
      description: 'Book early and save 20% on summer packages.',
      content: '<p>Plan ahead for your summer getaway and enjoy 20% off our premium packages when you book at least 60 days in advance.</p><p>This offer includes complimentary airport transfers and a welcome dinner.</p>',
      imageUrl: '/uploads/offer-summer-special.jpg',
      discount: 20,
      validFrom: '2023-01-01',
      validTo: '2023-04-30',
      featured: true,
      createdAt: '2023-01-01'
    },
    {
      id: 2,
      title: 'Honeymoon Package',
      slug: 'honeymoon-package',
      description: 'Special romantic package for newlyweds with exclusive perks.',
      content: '<p>Celebrate your new beginning with our special honeymoon package designed for romance and relaxation.</p><p>Enjoy a private candlelit dinner on the beach, couples spa treatment, and romantic room decoration.</p>',
      imageUrl: '/uploads/offer-honeymoon.jpg',
      discount: 15,
      validFrom: '2023-01-01',
      validTo: '2023-12-31',
      featured: true,
      createdAt: '2023-01-15'
    },
    {
      id: 3,
      title: 'Last Minute Getaway',
      slug: 'last-minute-getaway',
      description: 'Special rates for bookings made within 14 days of arrival.',
      content: '<p>Spontaneous travelers can take advantage of our last-minute offers with up to 25% off selected accommodations.</p><p>These special rates are available for bookings made within 14 days of arrival, subject to availability.</p>',
      imageUrl: '/uploads/offer-last-minute.jpg',
      discount: 25,
      validFrom: '2023-03-01',
      validTo: '2023-12-31',
      featured: false,
      createdAt: '2023-03-01'
    }
  ],
  inquiries: [
    {
      id: 1,
      name: 'John Smith',
      email: '<EMAIL>',
      phone: '****** 567 8901',
      subject: 'Honeymoon Package Inquiry',
      message: 'My fiancee and I are planning our honeymoon for next June. We are interested in the honeymoon package and would like more information about available dates and accommodations.',
      status: 'unread',
      createdAt: '2023-04-10'
    },
    {
      id: 2,
      name: 'Emma Johnson',
      email: '<EMAIL>',
      phone: '+44 20 1234 5678',
      subject: 'Group Booking Question',
      message: 'I am organizing a trip for 10 people in September. Do you offer any group discounts or special arrangements for larger parties?',
      status: 'read',
      createdAt: '2023-04-05'
    },
    {
      id: 3,
      name: 'Michael Wong',
      email: '<EMAIL>',
      phone: '+65 9123 4567',
      subject: 'Dietary Requirements',
      message: 'I have some specific dietary requirements (gluten-free and vegetarian). Can you confirm if the resorts you recommend can accommodate these needs?',
      status: 'replied',
      reply: 'Thank you for your inquiry. Yes, all of our partner resorts can accommodate gluten-free and vegetarian diets. When you make your booking, we will note your dietary requirements to ensure they are prepared for your stay.',
      createdAt: '2023-04-01'
    }
  ],
  media: [],
  settings: [
    { id: 1, group: 'general', key: 'siteTitle', value: 'Lux Voyage Travel Agency' },
    { id: 2, group: 'general', key: 'siteDescription', value: 'Luxury travel experiences in the Maldives' },
    { id: 3, group: 'general', key: 'logoUrl', value: '/images/logo.png' },
    { id: 4, group: 'general', key: 'faviconUrl', value: '/favicon.ico' },
    { id: 5, group: 'general', key: 'defaultLanguage', value: 'en' },
    { id: 6, group: 'general', key: 'maintenanceMode', value: 'false' },
    { id: 7, group: 'appearance', key: 'primaryColor', value: '#0099b8' },
    { id: 8, group: 'appearance', key: 'secondaryColor', value: '#ff9800' },
    { id: 9, group: 'appearance', key: 'fontFamily', value: 'Inter' },
    { id: 10, group: 'appearance', key: 'headingFontFamily', value: 'Cormorant Garamond' },
    { id: 11, group: 'appearance', key: 'buttonStyle', value: 'rounded' }
  ]
};

// Set up file uploads
const uploadsDir = path.join(__dirname, 'uploads');
if (!fs.existsSync(uploadsDir)) {
  fs.mkdirSync(uploadsDir, { recursive: true });
}

const storage = multer.diskStorage({
  destination: (req, file, cb) => {
    cb(null, uploadsDir);
  },
  filename: (req, file, cb) => {
    const uniqueSuffix = Date.now() + '-' + Math.round(Math.random() * 1E9);
    const ext = path.extname(file.originalname);
    cb(null, file.fieldname + '-' + uniqueSuffix + ext);
  }
});

const upload = multer({ storage });

// Serve static files from the uploads directory
app.use('/uploads', express.static(uploadsDir));

// Authentication middleware
const authenticateToken = (req, res, next) => {
  const authHeader = req.headers['authorization'];
  const token = authHeader && authHeader.split(' ')[1];

  if (!token) {
    console.log('No token provided in request:', req.path);
    return res.status(401).json({ message: 'Authentication required' });
  }

  jwt.verify(token, JWT_SECRET, (err, user) => {
    if (err) {
      console.log('Token verification failed:', err.message);
      if (err.name === 'TokenExpiredError') {
        return res.status(401).json({ message: 'Token expired, please login again', expired: true });
      }
      return res.status(403).json({ message: 'Invalid token', error: err.message });
    }

    req.user = user;
    next();
  });
};

// Admin role middleware
const requireAdmin = (req, res, next) => {
  if (req.user.role !== 'admin') {
    return res.status(403).json({ message: 'Admin privileges required' });
  }

  next();
};

// Auth routes
app.post('/api/auth/login', (req, res) => {
  const { username, password } = req.body;

  const user = db.users.find(u => u.username === username && u.password === password);

  if (!user) {
    return res.status(401).json({ message: 'Invalid username or password' });
  }

  // Create JWT token with extended expiration
  const token = jwt.sign(
    { id: user.id, username: user.username, role: user.role },
    JWT_SECRET,
    { expiresIn: JWT_EXPIRES_IN }
  );

  // Return user info (without password) and token
  const { password: _, ...userWithoutPassword } = user;

  res.json({
    message: 'Login successful',
    user: userWithoutPassword,
    token
  });
});

// User routes
app.get('/api/users', authenticateToken, requireAdmin, (req, res) => {
  // Return users without passwords
  const usersWithoutPasswords = db.users.map(user => {
    const { password, ...userWithoutPassword } = user;
    return userWithoutPassword;
  });

  res.json(usersWithoutPasswords);
});

app.get('/api/users/:id', authenticateToken, (req, res) => {
  const user = db.users.find(u => u.id === parseInt(req.params.id));

  if (!user) {
    return res.status(404).json({ message: 'User not found' });
  }

  // Check if the requesting user is the same as the requested user or is an admin
  if (req.user.id !== user.id && req.user.role !== 'admin') {
    return res.status(403).json({ message: 'Access denied' });
  }

  const { password, ...userWithoutPassword } = user;
  res.json(userWithoutPassword);
});

// Edit endpoint for users - returns the user in an editable form with additional metadata
app.get('/api/users/:id/edit', authenticateToken, (req, res) => {
  // Check if user is admin or editing their own account
  if (req.user.role !== 'admin' && req.user.id !== parseInt(req.params.id)) {
    return res.status(403).json({ message: 'Access denied. Admin privileges required or you can only edit your own account.' });
  }

  const user = db.users.find(u => u.id === parseInt(req.params.id));

  if (!user) {
    return res.status(404).json({ message: 'User not found' });
  }

  // Remove password from response
  const { password, ...userWithoutPassword } = user;

  // Get role options
  const roleOptions = ['admin', 'editor'];

  // Return the user with additional metadata for editing
  res.json({
    item: userWithoutPassword,
    metadata: {
      roleOptions,
      lastModified: new Date().toISOString(),
      canEdit: req.user.role === 'admin' || req.user.id === user.id,
      editableFields: req.user.role === 'admin'
        ? ['name', 'username', 'email', 'role']
        : ['name', 'email'],
      requiresPassword: false,
      canChangePassword: true
    }
  });
});

app.post('/api/users', authenticateToken, requireAdmin, (req, res) => {
  const { username, password, name, email, role } = req.body;

  // Check if username already exists
  if (db.users.some(u => u.username === username)) {
    return res.status(400).json({ message: 'Username already exists' });
  }

  const newUser = {
    id: db.users.length + 1,
    username,
    password,
    name,
    email,
    role: role || 'editor',
    createdAt: new Date().toISOString().split('T')[0]
  };

  db.users.push(newUser);

  const { password: _, ...userWithoutPassword } = newUser;
  res.status(201).json(userWithoutPassword);
});

// Settings routes
app.get('/api/settings', (req, res) => {
  res.json(db.settings);
});

app.get('/api/settings/:key', (req, res) => {
  const setting = db.settings.find(s => s.key === req.params.key);

  if (!setting) {
    return res.status(404).json({ message: 'Setting not found' });
  }

  res.json(setting);
});

// Edit endpoint for settings - returns the setting in an editable form with additional metadata
app.get('/api/settings/:key/edit', authenticateToken, requireAdmin, (req, res) => {
  const setting = db.settings.find(s => s.key === req.params.key);

  if (!setting) {
    return res.status(404).json({ message: 'Setting not found' });
  }

  // Determine value type and provide appropriate options
  let valueOptions = null;
  let valueType = 'text';

  if (setting.key === 'primaryColor' || setting.key === 'secondaryColor') {
    valueType = 'color';
  } else if (setting.key === 'maintenanceMode') {
    valueType = 'boolean';
    valueOptions = [
      { value: 'true', label: 'Enabled' },
      { value: 'false', label: 'Disabled' }
    ];
  } else if (setting.key === 'buttonStyle') {
    valueType = 'select';
    valueOptions = [
      { value: 'rounded', label: 'Rounded' },
      { value: 'square', label: 'Square' },
      { value: 'pill', label: 'Pill' }
    ];
  } else if (setting.key === 'fontFamily' || setting.key === 'headingFontFamily') {
    valueType = 'select';
    valueOptions = [
      { value: 'Inter', label: 'Inter' },
      { value: 'Roboto', label: 'Roboto' },
      { value: 'Open Sans', label: 'Open Sans' },
      { value: 'Cormorant Garamond', label: 'Cormorant Garamond' },
      { value: 'Playfair Display', label: 'Playfair Display' }
    ];
  }

  // Return the setting with additional metadata for editing
  res.json({
    item: setting,
    metadata: {
      valueType,
      valueOptions,
      lastModified: new Date().toISOString(),
      canEdit: true,
      group: setting.group,
      description: `Setting for ${setting.key} in the ${setting.group} group`
    }
  });
});

app.put('/api/settings/:key', authenticateToken, requireAdmin, (req, res) => {
  const { value } = req.body;
  const settingIndex = db.settings.findIndex(s => s.key === req.params.key);

  if (settingIndex === -1) {
    return res.status(404).json({ message: 'Setting not found' });
  }

  db.settings[settingIndex].value = value;
  res.json(db.settings[settingIndex]);
});

app.put('/api/settings', authenticateToken, requireAdmin, (req, res) => {
  const { settings } = req.body;

  if (!Array.isArray(settings)) {
    return res.status(400).json({ message: 'Settings must be an array' });
  }

  settings.forEach(setting => {
    const settingIndex = db.settings.findIndex(s => s.key === setting.key && s.group === setting.group);

    if (settingIndex !== -1) {
      db.settings[settingIndex].value = setting.value;
    } else {
      db.settings.push({
        id: db.settings.length + 1,
        group: setting.group,
        key: setting.key,
        value: setting.value
      });
    }
  });

  res.json({ message: 'Settings updated successfully' });
});

// Media routes
app.get('/api/media', (req, res) => {
  res.json(db.media);
});

app.get('/api/media/:id', (req, res) => {
  const media = db.media.find(m => m.id === parseInt(req.params.id));

  if (!media) {
    return res.status(404).json({ message: 'Media not found' });
  }

  res.json(media);
});

// Edit endpoint for media - returns the media item in an editable form with additional metadata
app.get('/api/media/:id/edit', authenticateToken, (req, res) => {
  const media = db.media.find(m => m.id === parseInt(req.params.id));

  if (!media) {
    return res.status(404).json({ message: 'Media not found' });
  }

  // Get media type options
  const typeOptions = ['image', 'video', 'document'];

  // Return the media with additional metadata for editing
  res.json({
    item: media,
    metadata: {
      typeOptions,
      lastModified: new Date().toISOString(),
      canEdit: true,
      editableFields: ['title', 'alt', 'description'],
      fileInfo: {
        url: media.url,
        type: media.type,
        size: media.size,
        dimensions: media.type === 'image' ? '1920x1080' : null
      }
    }
  });
});

app.post('/api/media/upload', authenticateToken, (req, res, next) => {
  console.log('Media upload request received, authenticated user:', req.user.username);
  next();
}, upload.single('file'), (req, res) => {
  if (!req.file) {
    console.log('No file in upload request');
    return res.status(400).json({ message: 'No file uploaded' });
  }

  console.log('File uploaded successfully:', req.file.filename);
  const fileUrl = `/uploads/${req.file.filename}`;
  const fileType = req.file.mimetype.startsWith('image/') ? 'image' :
                  req.file.mimetype.startsWith('video/') ? 'video' : 'document';

  const newMedia = {
    id: db.media.length + 1,
    title: req.body.title || req.file.originalname,
    alt: req.body.alt || req.file.originalname,
    description: req.body.description || '',
    url: fileUrl,
    type: fileType,
    size: `${(req.file.size / 1024).toFixed(2)} KB`,
    createdAt: new Date().toISOString().split('T')[0],
    createdBy: req.user.id
  };

  db.media.push(newMedia);
  console.log('Media item created:', newMedia.id);
  res.status(201).json(newMedia);
});

app.put('/api/media/:id', authenticateToken, (req, res) => {
  const mediaIndex = db.media.findIndex(m => m.id === parseInt(req.params.id));

  if (mediaIndex === -1) {
    return res.status(404).json({ message: 'Media not found' });
  }

  db.media[mediaIndex] = {
    ...db.media[mediaIndex],
    title: req.body.title || db.media[mediaIndex].title,
    alt: req.body.alt || db.media[mediaIndex].alt,
    description: req.body.description || db.media[mediaIndex].description
  };

  res.json(db.media[mediaIndex]);
});

app.delete('/api/media/:id', authenticateToken, requireAdmin, (req, res) => {
  const mediaIndex = db.media.findIndex(m => m.id === parseInt(req.params.id));

  if (mediaIndex === -1) {
    return res.status(404).json({ message: 'Media not found' });
  }

  // Delete the file from the uploads directory
  const filePath = path.join(__dirname, db.media[mediaIndex].url);
  if (fs.existsSync(filePath)) {
    fs.unlinkSync(filePath);
  }

  db.media.splice(mediaIndex, 1);
  res.json({ message: 'Media deleted successfully' });
});

// Destination routes
app.get('/api/destinations', (req, res) => {
  res.json(db.destinations);
});

app.get('/api/destinations/featured', (req, res) => {
  const featuredDestinations = db.destinations.filter(d => d.featured);
  res.json(featuredDestinations);
});

app.get('/api/destinations/category/:category', (req, res) => {
  const { category } = req.params;
  const destinations = db.destinations.filter(d => d.category === category);
  res.json(destinations);
});

app.get('/api/destinations/:id', (req, res) => {
  const destination = db.destinations.find(d => d.id === parseInt(req.params.id));

  if (!destination) {
    return res.status(404).json({ message: 'Destination not found' });
  }

  res.json(destination);
});

// Edit endpoint for destinations - returns the item in an editable form with additional metadata
app.get('/api/destinations/:id/edit', authenticateToken, (req, res) => {
  const destination = db.destinations.find(d => d.id === parseInt(req.params.id));

  if (!destination) {
    return res.status(404).json({ message: 'Destination not found' });
  }

  // Get available categories for dropdown options
  const categories = [...new Set(db.destinations.map(d => d.category))];

  // Return the destination with additional metadata for editing
  res.json({
    item: destination,
    metadata: {
      categories,
      lastModified: new Date().toISOString(),
      canEdit: true,
      editableFields: [
        'title', 'slug', 'description', 'content', 'imageUrl', 'category', 'featured'
      ]
    }
  });
});

app.get('/api/destinations/slug/:slug', (req, res) => {
  const destination = db.destinations.find(d => d.slug === req.params.slug);

  if (!destination) {
    return res.status(404).json({ message: 'Destination not found' });
  }

  res.json(destination);
});

app.post('/api/destinations', authenticateToken, (req, res) => {
  const { title, slug, description, content, imageUrl, category, featured } = req.body;

  // Check if slug already exists
  if (db.destinations.some(d => d.slug === slug)) {
    return res.status(400).json({ message: 'Slug already exists' });
  }

  const newDestination = {
    id: db.destinations.length > 0 ? Math.max(...db.destinations.map(d => d.id)) + 1 : 1,
    title,
    slug,
    description,
    content,
    imageUrl,
    category,
    featured: featured || false,
    createdAt: new Date().toISOString().split('T')[0]
  };

  db.destinations.push(newDestination);
  res.status(201).json(newDestination);
});

app.put('/api/destinations/:id', authenticateToken, (req, res) => {
  const destinationIndex = db.destinations.findIndex(d => d.id === parseInt(req.params.id));

  if (destinationIndex === -1) {
    return res.status(404).json({ message: 'Destination not found' });
  }

  const { title, slug, description, content, imageUrl, category, featured } = req.body;

  // Check if slug already exists and it's not the current destination
  if (slug !== db.destinations[destinationIndex].slug &&
      db.destinations.some(d => d.slug === slug)) {
    return res.status(400).json({ message: 'Slug already exists' });
  }

  db.destinations[destinationIndex] = {
    ...db.destinations[destinationIndex],
    title: title || db.destinations[destinationIndex].title,
    slug: slug || db.destinations[destinationIndex].slug,
    description: description || db.destinations[destinationIndex].description,
    content: content || db.destinations[destinationIndex].content,
    imageUrl: imageUrl || db.destinations[destinationIndex].imageUrl,
    category: category || db.destinations[destinationIndex].category,
    featured: featured !== undefined ? featured : db.destinations[destinationIndex].featured
  };

  res.json(db.destinations[destinationIndex]);
});

app.delete('/api/destinations/:id', authenticateToken, requireAdmin, (req, res) => {
  const destinationIndex = db.destinations.findIndex(d => d.id === parseInt(req.params.id));

  if (destinationIndex === -1) {
    return res.status(404).json({ message: 'Destination not found' });
  }

  db.destinations.splice(destinationIndex, 1);
  res.json({ message: 'Destination deleted successfully' });
});

// Experience routes
app.get('/api/experiences', (req, res) => {
  res.json(db.experiences);
});

app.get('/api/experiences/featured', (req, res) => {
  const featuredExperiences = db.experiences.filter(e => e.featured);
  res.json(featuredExperiences);
});

app.get('/api/experiences/category/:category', (req, res) => {
  const { category } = req.params;
  const experiences = db.experiences.filter(e => e.category === category);
  res.json(experiences);
});

app.get('/api/experiences/:id', (req, res) => {
  const experience = db.experiences.find(e => e.id === parseInt(req.params.id));

  if (!experience) {
    return res.status(404).json({ message: 'Experience not found' });
  }

  res.json(experience);
});

// Edit endpoint for experiences - returns the item in an editable form with additional metadata
app.get('/api/experiences/:id/edit', authenticateToken, (req, res) => {
  const experience = db.experiences.find(e => e.id === parseInt(req.params.id));

  if (!experience) {
    return res.status(404).json({ message: 'Experience not found' });
  }

  // Get available categories for dropdown options
  const categories = [...new Set(db.experiences.map(e => e.category))];

  // Get duration options
  const durationOptions = ['1 hour', '2 hours', '3 hours', '4 hours', 'Half day', 'Full day'];

  // Return the experience with additional metadata for editing
  res.json({
    item: experience,
    metadata: {
      categories,
      durationOptions,
      lastModified: new Date().toISOString(),
      canEdit: true,
      editableFields: [
        'title', 'slug', 'description', 'content', 'imageUrl', 'category',
        'duration', 'price', 'featured'
      ]
    }
  });
});

app.get('/api/experiences/slug/:slug', (req, res) => {
  const experience = db.experiences.find(e => e.slug === req.params.slug);

  if (!experience) {
    return res.status(404).json({ message: 'Experience not found' });
  }

  res.json(experience);
});

app.post('/api/experiences', authenticateToken, (req, res) => {
  const { title, slug, description, content, imageUrl, category, duration, price, featured } = req.body;

  // Check if slug already exists
  if (db.experiences.some(e => e.slug === slug)) {
    return res.status(400).json({ message: 'Slug already exists' });
  }

  const newExperience = {
    id: db.experiences.length > 0 ? Math.max(...db.experiences.map(e => e.id)) + 1 : 1,
    title,
    slug,
    description,
    content,
    imageUrl,
    category,
    duration,
    price: parseFloat(price),
    featured: featured || false,
    createdAt: new Date().toISOString().split('T')[0]
  };

  db.experiences.push(newExperience);
  res.status(201).json(newExperience);
});

app.put('/api/experiences/:id', authenticateToken, (req, res) => {
  const experienceIndex = db.experiences.findIndex(e => e.id === parseInt(req.params.id));

  if (experienceIndex === -1) {
    return res.status(404).json({ message: 'Experience not found' });
  }

  const { title, slug, description, content, imageUrl, category, duration, price, featured } = req.body;

  // Check if slug already exists and it's not the current experience
  if (slug !== db.experiences[experienceIndex].slug &&
      db.experiences.some(e => e.slug === slug)) {
    return res.status(400).json({ message: 'Slug already exists' });
  }

  db.experiences[experienceIndex] = {
    ...db.experiences[experienceIndex],
    title: title || db.experiences[experienceIndex].title,
    slug: slug || db.experiences[experienceIndex].slug,
    description: description || db.experiences[experienceIndex].description,
    content: content || db.experiences[experienceIndex].content,
    imageUrl: imageUrl || db.experiences[experienceIndex].imageUrl,
    category: category || db.experiences[experienceIndex].category,
    duration: duration || db.experiences[experienceIndex].duration,
    price: price !== undefined ? parseFloat(price) : db.experiences[experienceIndex].price,
    featured: featured !== undefined ? featured : db.experiences[experienceIndex].featured
  };

  res.json(db.experiences[experienceIndex]);
});

app.delete('/api/experiences/:id', authenticateToken, requireAdmin, (req, res) => {
  const experienceIndex = db.experiences.findIndex(e => e.id === parseInt(req.params.id));

  if (experienceIndex === -1) {
    return res.status(404).json({ message: 'Experience not found' });
  }

  db.experiences.splice(experienceIndex, 1);
  res.json({ message: 'Experience deleted successfully' });
});

// Offers routes
app.get('/api/offers', (req, res) => {
  res.json(db.offers);
});

app.get('/api/offers/active', (req, res) => {
  const today = new Date().toISOString().split('T')[0];
  const activeOffers = db.offers.filter(o =>
    o.validFrom <= today && o.validTo >= today
  );
  res.json(activeOffers);
});

app.get('/api/offers/featured', (req, res) => {
  const featuredOffers = db.offers.filter(o => o.featured);
  res.json(featuredOffers);
});

app.get('/api/offers/:id', (req, res) => {
  const offer = db.offers.find(o => o.id === parseInt(req.params.id));

  if (!offer) {
    return res.status(404).json({ message: 'Offer not found' });
  }

  res.json(offer);
});

// Edit endpoint for offers - returns the item in an editable form with additional metadata
app.get('/api/offers/:id/edit', authenticateToken, (req, res) => {
  const offer = db.offers.find(o => o.id === parseInt(req.params.id));

  if (!offer) {
    return res.status(404).json({ message: 'Offer not found' });
  }

  // Get discount options
  const discountOptions = [5, 10, 15, 20, 25, 30, 40, 50];

  // Return the offer with additional metadata for editing
  res.json({
    item: offer,
    metadata: {
      discountOptions,
      lastModified: new Date().toISOString(),
      canEdit: true,
      editableFields: [
        'title', 'slug', 'description', 'content', 'imageUrl',
        'discount', 'validFrom', 'validTo', 'featured'
      ],
      validationRules: {
        validTo: 'must be after validFrom',
        discount: 'must be between 1 and 100'
      }
    }
  });
});

app.get('/api/offers/slug/:slug', (req, res) => {
  const offer = db.offers.find(o => o.slug === req.params.slug);

  if (!offer) {
    return res.status(404).json({ message: 'Offer not found' });
  }

  res.json(offer);
});

app.post('/api/offers', authenticateToken, (req, res) => {
  const { title, slug, description, content, imageUrl, discount, validFrom, validTo, featured } = req.body;

  // Check if slug already exists
  if (db.offers.some(o => o.slug === slug)) {
    return res.status(400).json({ message: 'Slug already exists' });
  }

  const newOffer = {
    id: db.offers.length > 0 ? Math.max(...db.offers.map(o => o.id)) + 1 : 1,
    title,
    slug,
    description,
    content,
    imageUrl,
    discount: parseFloat(discount),
    validFrom,
    validTo,
    featured: featured || false,
    createdAt: new Date().toISOString().split('T')[0]
  };

  db.offers.push(newOffer);
  res.status(201).json(newOffer);
});

app.put('/api/offers/:id', authenticateToken, (req, res) => {
  const offerIndex = db.offers.findIndex(o => o.id === parseInt(req.params.id));

  if (offerIndex === -1) {
    return res.status(404).json({ message: 'Offer not found' });
  }

  const { title, slug, description, content, imageUrl, discount, validFrom, validTo, featured } = req.body;

  // Check if slug already exists and it's not the current offer
  if (slug !== db.offers[offerIndex].slug &&
      db.offers.some(o => o.slug === slug)) {
    return res.status(400).json({ message: 'Slug already exists' });
  }

  db.offers[offerIndex] = {
    ...db.offers[offerIndex],
    title: title || db.offers[offerIndex].title,
    slug: slug || db.offers[offerIndex].slug,
    description: description || db.offers[offerIndex].description,
    content: content || db.offers[offerIndex].content,
    imageUrl: imageUrl || db.offers[offerIndex].imageUrl,
    discount: discount !== undefined ? parseFloat(discount) : db.offers[offerIndex].discount,
    validFrom: validFrom || db.offers[offerIndex].validFrom,
    validTo: validTo || db.offers[offerIndex].validTo,
    featured: featured !== undefined ? featured : db.offers[offerIndex].featured
  };

  res.json(db.offers[offerIndex]);
});

app.delete('/api/offers/:id', authenticateToken, requireAdmin, (req, res) => {
  const offerIndex = db.offers.findIndex(o => o.id === parseInt(req.params.id));

  if (offerIndex === -1) {
    return res.status(404).json({ message: 'Offer not found' });
  }

  db.offers.splice(offerIndex, 1);
  res.json({ message: 'Offer deleted successfully' });
});

// Inquiries routes
app.get('/api/inquiries', authenticateToken, (req, res) => {
  res.json(db.inquiries);
});

app.get('/api/inquiries/unread', authenticateToken, (req, res) => {
  const unreadInquiries = db.inquiries.filter(i => i.status === 'unread');
  res.json(unreadInquiries);
});

app.get('/api/inquiries/:id', authenticateToken, (req, res) => {
  const inquiry = db.inquiries.find(i => i.id === parseInt(req.params.id));

  if (!inquiry) {
    return res.status(404).json({ message: 'Inquiry not found' });
  }

  res.json(inquiry);
});

// Edit endpoint for inquiries - returns the inquiry in an editable form with additional metadata
app.get('/api/inquiries/:id/edit', authenticateToken, (req, res) => {
  const inquiry = db.inquiries.find(i => i.id === parseInt(req.params.id));

  if (!inquiry) {
    return res.status(404).json({ message: 'Inquiry not found' });
  }

  // Get status options
  const statusOptions = ['unread', 'read', 'replied'];

  // Return the inquiry with additional metadata for editing
  res.json({
    item: inquiry,
    metadata: {
      statusOptions,
      lastModified: new Date().toISOString(),
      canEdit: true,
      editableFields: ['status', 'reply'],
      canReply: inquiry.status !== 'replied',
      replyTemplate: `Dear ${inquiry.name},\n\nThank you for your inquiry about ${inquiry.subject}.\n\n[Your response here]\n\nBest regards,\nLux Voyage Team`
    }
  });
});

app.post('/api/inquiries', (req, res) => {
  const { name, email, phone, subject, message } = req.body;

  // Validate required fields
  if (!name || !email || !message) {
    return res.status(400).json({ message: 'Name, email, and message are required' });
  }

  const newInquiry = {
    id: db.inquiries.length > 0 ? Math.max(...db.inquiries.map(i => i.id)) + 1 : 1,
    name,
    email,
    phone: phone || '',
    subject: subject || 'General Inquiry',
    message,
    status: 'unread',
    createdAt: new Date().toISOString().split('T')[0]
  };

  db.inquiries.push(newInquiry);
  res.status(201).json(newInquiry);
});

app.put('/api/inquiries/:id/read', authenticateToken, (req, res) => {
  const inquiryIndex = db.inquiries.findIndex(i => i.id === parseInt(req.params.id));

  if (inquiryIndex === -1) {
    return res.status(404).json({ message: 'Inquiry not found' });
  }

  db.inquiries[inquiryIndex].status = 'read';
  res.json(db.inquiries[inquiryIndex]);
});

app.post('/api/inquiries/:id/reply', authenticateToken, (req, res) => {
  const inquiryIndex = db.inquiries.findIndex(i => i.id === parseInt(req.params.id));

  if (inquiryIndex === -1) {
    return res.status(404).json({ message: 'Inquiry not found' });
  }

  const { reply } = req.body;

  if (!reply) {
    return res.status(400).json({ message: 'Reply message is required' });
  }

  db.inquiries[inquiryIndex].reply = reply;
  db.inquiries[inquiryIndex].status = 'replied';
  db.inquiries[inquiryIndex].repliedAt = new Date().toISOString().split('T')[0];
  db.inquiries[inquiryIndex].repliedBy = req.user.id;

  res.json(db.inquiries[inquiryIndex]);
});

app.delete('/api/inquiries/:id', authenticateToken, requireAdmin, (req, res) => {
  const inquiryIndex = db.inquiries.findIndex(i => i.id === parseInt(req.params.id));

  if (inquiryIndex === -1) {
    return res.status(404).json({ message: 'Inquiry not found' });
  }

  db.inquiries.splice(inquiryIndex, 1);
  res.json({ message: 'Inquiry deleted successfully' });
});

// Start the server
app.listen(PORT, () => {
  console.log(`Server running on port ${PORT}`);
});
