const express = require('express');
const cors = require('cors');
const bodyParser = require('body-parser');
const jwt = require('jsonwebtoken');
const multer = require('multer');
const path = require('path');
const fs = require('fs');
const Database = require('better-sqlite3');
const { Server } = require('socket.io');
const http = require('http');
const bcrypt = require('bcryptjs');
const helmet = require('helmet');
const compression = require('compression');
const rateLimit = require('express-rate-limit');
const morgan = require('morgan');
const enforce = require('express-sslify');
require('dotenv').config();

// Initialize database
const { initializeDatabase } = require('./database/init');
const models = require('./database/models');

// Create Express app
const app = express();
const PORT = process.env.PORT || 5000;

// Create HTTP server for Socket.IO
const server = http.createServer(app);

// Security middleware
if (process.env.NODE_ENV === 'production') {
  app.use(enforce.HTTPS({ trustProtoHeader: true }));
}
app.use(helmet());
app.use(compression());

// Rate limiting
const limiter = rateLimit({
  windowMs: 15 * 60 * 1000, // 15 minutes
  max: 100, // Limit each IP to 100 requests per windowMs
  message: 'Too many requests from this IP, please try again later.'
});
app.use('/api/', limiter);

// Logging middleware
app.use(morgan('combined'));

// Initialize Socket.IO with configurable CORS
const io = new Server(server, {
  cors: {
    origin: process.env.CORS_ORIGIN || "http://localhost:3000",
    methods: ["GET", "POST"]
  }
});

// Middleware
app.use(cors({
  origin: process.env.CORS_ORIGIN || "http://localhost:3000",
  methods: ['GET', 'POST', 'PUT', 'DELETE'],
  allowedHeaders: ['Content-Type', 'Authorization']
}));
app.use(bodyParser.json());
app.use(bodyParser.urlencoded({ extended: true }));

// JWT Configuration
const JWT_SECRET = process.env.JWT_SECRET || 'your-secret-key';
const JWT_EXPIRES_IN = process.env.JWT_EXPIRES_IN || '7d';

// File upload configuration
const MAX_FILE_SIZE = parseInt(process.env.MAX_FILE_SIZE) || 5242880; // 5MB default
const ALLOWED_FILE_TYPES = (process.env.ALLOWED_FILE_TYPES || 'image/jpeg,image/png,image/gif').split(',');

// Socket.IO connection handling
io.on('connection', (socket) => {
  console.log(`Client connected: ${socket.id}`);

  socket.on('disconnect', () => {
    console.log(`Client disconnected: ${socket.id}`);
  });

  // Join admin room for real-time updates
  socket.on('join-admin', () => {
    socket.join('admin');
    console.log(`Client joined admin room: ${socket.id}`);
  });
});

// Helper function to emit real-time updates
const emitUpdate = (event, data) => {
  io.to('admin').emit(event, data);
};

// Set up file uploads with restrictions
const uploadsDir = path.join(__dirname, 'uploads');
if (!fs.existsSync(uploadsDir)) {
  fs.mkdirSync(uploadsDir, { recursive: true });
}

const storage = multer.diskStorage({
  destination: (req, file, cb) => {
    cb(null, uploadsDir);
  },
  filename: (req, file, cb) => {
    const uniqueSuffix = Date.now() + '-' + Math.round(Math.random() * 1E9);
    const ext = path.extname(file.originalname);
    cb(null, file.fieldname + '-' + uniqueSuffix + ext);
  }
});

const fileFilter = (req, file, cb) => {
  if (ALLOWED_FILE_TYPES.includes(file.mimetype)) {
    cb(null, true);
  } else {
    cb(new Error('Invalid file type. Only images are allowed.'), false);
  }
};

const upload = multer({
  storage,
  limits: {
    fileSize: MAX_FILE_SIZE
  },
  fileFilter
});

// Serve static files from the uploads directory
app.use('/uploads', express.static(uploadsDir));

// Authentication middleware
const authenticateToken = (req, res, next) => {
  const authHeader = req.headers['authorization'];
  const token = authHeader && authHeader.split(' ')[1];

  if (!token) {
    console.log('No token provided in request:', req.path);
    return res.status(401).json({ message: 'Authentication required' });
  }

  jwt.verify(token, JWT_SECRET, (err, user) => {
    if (err) {
      console.log('Token verification failed:', err.message);
      if (err.name === 'TokenExpiredError') {
        return res.status(401).json({ message: 'Token expired, please login again', expired: true });
      }
      return res.status(403).json({ message: 'Invalid token', error: err.message });
    }

    req.user = user;
    next();
  });
};

// Admin role middleware
const requireAdmin = (req, res, next) => {
  if (req.user.role !== 'admin') {
    return res.status(403).json({ message: 'Admin privileges required' });
  }
  next();
};

// Auth routes
app.post('/api/auth/login', async (req, res) => {
  try {
    const { username, password } = req.body;

    const user = models.User.getByUsername(username);

    if (!user) {
      return res.status(401).json({ message: 'Invalid username or password' });
    }

    // Compare password with hashed password
    const isValidPassword = await bcrypt.compare(password, user.password);
    if (!isValidPassword) {
      return res.status(401).json({ message: 'Invalid username or password' });
    }

    // Create JWT token
    const token = jwt.sign(
      { id: user.id, username: user.username, role: user.role },
      JWT_SECRET,
      { expiresIn: JWT_EXPIRES_IN }
    );

    // Return user info (without password) and token
    const { password: _, ...userWithoutPassword } = user;

    res.json({
      message: 'Login successful',
      user: userWithoutPassword,
      token
    });
  } catch (error) {
    console.error('Login error:', error);
    res.status(500).json({ message: 'Internal server error' });
  }
});

// User routes
app.get('/api/users', authenticateToken, requireAdmin, (req, res) => {
  try {
    const users = models.User.getAll();
    // Return users without passwords
    const usersWithoutPasswords = users.map(user => {
      const { password, ...userWithoutPassword } = user;
      return userWithoutPassword;
    });

    res.json(usersWithoutPasswords);
  } catch (error) {
    console.error('Error fetching users:', error);
    res.status(500).json({ message: 'Internal server error' });
  }
});

app.get('/api/users/:id', authenticateToken, (req, res) => {
  try {
    const user = models.User.getById(parseInt(req.params.id));

    if (!user) {
      return res.status(404).json({ message: 'User not found' });
    }

    // Check if the requesting user is the same as the requested user or is an admin
    if (req.user.id !== user.id && req.user.role !== 'admin') {
      return res.status(403).json({ message: 'Access denied' });
    }

    const { password, ...userWithoutPassword } = user;
    res.json(userWithoutPassword);
  } catch (error) {
    console.error('Error fetching user:', error);
    res.status(500).json({ message: 'Internal server error' });
  }
});

app.get('/api/users/:id/edit', authenticateToken, (req, res) => {
  try {
    // Check if user is admin or editing their own account
    if (req.user.role !== 'admin' && req.user.id !== parseInt(req.params.id)) {
      return res.status(403).json({ message: 'Access denied. Admin privileges required or you can only edit your own account.' });
    }

    const user = models.User.getById(parseInt(req.params.id));

    if (!user) {
      return res.status(404).json({ message: 'User not found' });
    }

    // Remove password from response
    const { password, ...userWithoutPassword } = user;

    // Get role options
    const roleOptions = ['admin', 'editor'];

    // Return the user with additional metadata for editing
    res.json({
      item: userWithoutPassword,
      metadata: {
        roleOptions,
        lastModified: new Date().toISOString(),
        canEdit: req.user.role === 'admin' || req.user.id === user.id,
        editableFields: req.user.role === 'admin'
          ? ['name', 'username', 'email', 'role']
          : ['name', 'email'],
        requiresPassword: false,
        canChangePassword: true
      }
    });
  } catch (error) {
    console.error('Error fetching user for edit:', error);
    res.status(500).json({ message: 'Internal server error' });
  }
});

app.post('/api/users', authenticateToken, requireAdmin, (req, res) => {
  try {
    const { username, password, name, email, role } = req.body;

    // Check if username already exists
    const existingUser = models.User.getByUsername(username);
    if (existingUser) {
      return res.status(400).json({ message: 'Username already exists' });
    }

    const newUser = models.User.create({
      username,
      password,
      name,
      email,
      role: role || 'editor'
    });

    const { password: _, ...userWithoutPassword } = newUser;

    // Emit real-time update
    emitUpdate('user-created', userWithoutPassword);

    res.status(201).json(userWithoutPassword);
  } catch (error) {
    console.error('Error creating user:', error);
    res.status(500).json({ message: 'Internal server error' });
  }
});

app.put('/api/users/:id', authenticateToken, (req, res) => {
  try {
    const userId = parseInt(req.params.id);

    // Check permissions
    if (req.user.role !== 'admin' && req.user.id !== userId) {
      return res.status(403).json({ message: 'Access denied' });
    }

    const updatedUser = models.User.update(userId, req.body);

    if (!updatedUser) {
      return res.status(404).json({ message: 'User not found' });
    }

    const { password, ...userWithoutPassword } = updatedUser;

    // Emit real-time update
    emitUpdate('user-updated', userWithoutPassword);

    res.json(userWithoutPassword);
  } catch (error) {
    console.error('Error updating user:', error);
    res.status(500).json({ message: 'Internal server error' });
  }
});

app.delete('/api/users/:id', authenticateToken, requireAdmin, (req, res) => {
  try {
    const userId = parseInt(req.params.id);

    // Prevent admin from deleting themselves
    if (req.user.id === userId) {
      return res.status(400).json({ message: 'Cannot delete your own account' });
    }

    const deleted = models.User.deleteById(userId);

    if (!deleted) {
      return res.status(404).json({ message: 'User not found' });
    }

    // Emit real-time update
    emitUpdate('user-deleted', { id: userId });

    res.json({ message: 'User deleted successfully' });
  } catch (error) {
    console.error('Error deleting user:', error);
    res.status(500).json({ message: 'Internal server error' });
  }
});

// Destination routes
app.get('/api/destinations', (req, res) => {
  try {
    const destinations = models.Destination.getAll();
    res.json(destinations);
  } catch (error) {
    console.error('Error fetching destinations:', error);
    res.status(500).json({ message: 'Internal server error' });
  }
});

app.get('/api/destinations/featured', (req, res) => {
  try {
    const featuredDestinations = models.Destination.getFeatured();
    res.json(featuredDestinations);
  } catch (error) {
    console.error('Error fetching featured destinations:', error);
    res.status(500).json({ message: 'Internal server error' });
  }
});

app.get('/api/destinations/category/:category', (req, res) => {
  try {
    const { category } = req.params;
    const destinations = models.Destination.getByCategory(category);
    res.json(destinations);
  } catch (error) {
    console.error('Error fetching destinations by category:', error);
    res.status(500).json({ message: 'Internal server error' });
  }
});

app.get('/api/destinations/:id', (req, res) => {
  try {
    const destination = models.Destination.getById(parseInt(req.params.id));

    if (!destination) {
      return res.status(404).json({ message: 'Destination not found' });
    }

    res.json(destination);
  } catch (error) {
    console.error('Error fetching destination:', error);
    res.status(500).json({ message: 'Internal server error' });
  }
});

app.get('/api/destinations/slug/:slug', (req, res) => {
  try {
    const destination = models.Destination.getBySlug(req.params.slug);

    if (!destination) {
      return res.status(404).json({ message: 'Destination not found' });
    }

    res.json(destination);
  } catch (error) {
    console.error('Error fetching destination by slug:', error);
    res.status(500).json({ message: 'Internal server error' });
  }
});

app.get('/api/destinations/:id/edit', authenticateToken, (req, res) => {
  try {
    const destination = models.Destination.getById(parseInt(req.params.id));

    if (!destination) {
      return res.status(404).json({ message: 'Destination not found' });
    }

    // Get available categories for dropdown options
    const allDestinations = models.Destination.getAll();
    const categories = [...new Set(allDestinations.map(d => d.category))];

    // Return the destination with additional metadata for editing
    res.json({
      item: destination,
      metadata: {
        categories,
        lastModified: new Date().toISOString(),
        canEdit: true,
        editableFields: [
          'title', 'slug', 'description', 'content', 'image_url', 'category', 'featured'
        ]
      }
    });
  } catch (error) {
    console.error('Error fetching destination for edit:', error);
    res.status(500).json({ message: 'Internal server error' });
  }
});

app.post('/api/destinations', authenticateToken, (req, res) => {
  try {
    const { title, slug, description, content, image_url, category, featured } = req.body;

    // Check if slug already exists
    const existingDestination = models.Destination.getBySlug(slug);
    if (existingDestination) {
      return res.status(400).json({ message: 'Slug already exists' });
    }

    const newDestination = models.Destination.create({
      title,
      slug,
      description,
      content,
      image_url,
      category,
      featured: featured || false
    });

    // Emit real-time update
    emitUpdate('destination-created', newDestination);

    res.status(201).json(newDestination);
  } catch (error) {
    console.error('Error creating destination:', error);
    res.status(500).json({ message: 'Internal server error' });
  }
});

app.put('/api/destinations/:id', authenticateToken, (req, res) => {
  try {
    const destinationId = parseInt(req.params.id);
    const { title, slug, description, content, image_url, category, featured } = req.body;

    // Check if slug already exists and it's not the current destination
    const existingDestination = models.Destination.getBySlug(slug);
    if (existingDestination && existingDestination.id !== destinationId) {
      return res.status(400).json({ message: 'Slug already exists' });
    }

    const updatedDestination = models.Destination.update(destinationId, {
      title,
      slug,
      description,
      content,
      image_url,
      category,
      featured
    });

    if (!updatedDestination) {
      return res.status(404).json({ message: 'Destination not found' });
    }

    // Emit real-time update
    emitUpdate('destination-updated', updatedDestination);

    res.json(updatedDestination);
  } catch (error) {
    console.error('Error updating destination:', error);
    res.status(500).json({ message: 'Internal server error' });
  }
});

app.delete('/api/destinations/:id', authenticateToken, requireAdmin, (req, res) => {
  try {
    const destinationId = parseInt(req.params.id);

    const deleted = models.Destination.deleteById(destinationId);

    if (!deleted) {
      return res.status(404).json({ message: 'Destination not found' });
    }

    // Emit real-time update
    emitUpdate('destination-deleted', { id: destinationId });

    res.json({ message: 'Destination deleted successfully' });
  } catch (error) {
    console.error('Error deleting destination:', error);
    res.status(500).json({ message: 'Internal server error' });
  }
});

// Experience routes
app.get('/api/experiences', (req, res) => {
  try {
    const experiences = models.Experience.getAll();
    res.json(experiences);
  } catch (error) {
    console.error('Error fetching experiences:', error);
    res.status(500).json({ message: 'Internal server error' });
  }
});

app.get('/api/experiences/featured', (req, res) => {
  try {
    const featuredExperiences = models.Experience.getFeatured();
    res.json(featuredExperiences);
  } catch (error) {
    console.error('Error fetching featured experiences:', error);
    res.status(500).json({ message: 'Internal server error' });
  }
});

app.get('/api/experiences/category/:category', (req, res) => {
  try {
    const { category } = req.params;
    const experiences = models.Experience.getByCategory(category);
    res.json(experiences);
  } catch (error) {
    console.error('Error fetching experiences by category:', error);
    res.status(500).json({ message: 'Internal server error' });
  }
});

app.get('/api/experiences/:id', (req, res) => {
  try {
    const experience = models.Experience.getById(parseInt(req.params.id));

    if (!experience) {
      return res.status(404).json({ message: 'Experience not found' });
    }

    res.json(experience);
  } catch (error) {
    console.error('Error fetching experience:', error);
    res.status(500).json({ message: 'Internal server error' });
  }
});

app.get('/api/experiences/slug/:slug', (req, res) => {
  try {
    const experience = models.Experience.getBySlug(req.params.slug);

    if (!experience) {
      return res.status(404).json({ message: 'Experience not found' });
    }

    res.json(experience);
  } catch (error) {
    console.error('Error fetching experience by slug:', error);
    res.status(500).json({ message: 'Internal server error' });
  }
});

app.get('/api/experiences/:id/edit', authenticateToken, (req, res) => {
  try {
    const experience = models.Experience.getById(parseInt(req.params.id));

    if (!experience) {
      return res.status(404).json({ message: 'Experience not found' });
    }

    // Get available categories for dropdown options
    const allExperiences = models.Experience.getAll();
    const categories = [...new Set(allExperiences.map(e => e.category))];

    // Get duration options
    const durationOptions = ['1 hour', '2 hours', '3 hours', '4 hours', 'Half day', 'Full day'];

    // Return the experience with additional metadata for editing
    res.json({
      item: experience,
      metadata: {
        categories,
        durationOptions,
        lastModified: new Date().toISOString(),
        canEdit: true,
        editableFields: [
          'title', 'slug', 'description', 'content', 'image_url', 'category',
          'duration', 'price', 'featured'
        ]
      }
    });
  } catch (error) {
    console.error('Error fetching experience for edit:', error);
    res.status(500).json({ message: 'Internal server error' });
  }
});

app.post('/api/experiences', authenticateToken, (req, res) => {
  try {
    const { title, slug, description, content, image_url, category, duration, price, featured } = req.body;

    // Check if slug already exists
    const existingExperience = models.Experience.getBySlug(slug);
    if (existingExperience) {
      return res.status(400).json({ message: 'Slug already exists' });
    }

    const newExperience = models.Experience.create({
      title,
      slug,
      description,
      content,
      image_url,
      category,
      duration,
      price: parseFloat(price),
      featured: featured || false
    });

    // Emit real-time update
    emitUpdate('experience-created', newExperience);

    res.status(201).json(newExperience);
  } catch (error) {
    console.error('Error creating experience:', error);
    res.status(500).json({ message: 'Internal server error' });
  }
});

app.put('/api/experiences/:id', authenticateToken, (req, res) => {
  try {
    const experienceId = parseInt(req.params.id);
    const { title, slug, description, content, image_url, category, duration, price, featured } = req.body;

    // Check if slug already exists and it's not the current experience
    const existingExperience = models.Experience.getBySlug(slug);
    if (existingExperience && existingExperience.id !== experienceId) {
      return res.status(400).json({ message: 'Slug already exists' });
    }

    const updatedExperience = models.Experience.update(experienceId, {
      title,
      slug,
      description,
      content,
      image_url,
      category,
      duration,
      price: price !== undefined ? parseFloat(price) : undefined,
      featured
    });

    if (!updatedExperience) {
      return res.status(404).json({ message: 'Experience not found' });
    }

    // Emit real-time update
    emitUpdate('experience-updated', updatedExperience);

    res.json(updatedExperience);
  } catch (error) {
    console.error('Error updating experience:', error);
    res.status(500).json({ message: 'Internal server error' });
  }
});

app.delete('/api/experiences/:id', authenticateToken, requireAdmin, (req, res) => {
  try {
    const experienceId = parseInt(req.params.id);

    const deleted = models.Experience.deleteById(experienceId);

    if (!deleted) {
      return res.status(404).json({ message: 'Experience not found' });
    }

    // Emit real-time update
    emitUpdate('experience-deleted', { id: experienceId });

    res.json({ message: 'Experience deleted successfully' });
  } catch (error) {
    console.error('Error deleting experience:', error);
    res.status(500).json({ message: 'Internal server error' });
  }
});

// Offer routes
app.get('/api/offers', (req, res) => {
  try {
    const offers = models.Offer.getAll();
    res.json(offers);
  } catch (error) {
    console.error('Error fetching offers:', error);
    res.status(500).json({ message: 'Internal server error' });
  }
});

app.get('/api/offers/active', (req, res) => {
  try {
    const activeOffers = models.Offer.getActive();
    res.json(activeOffers);
  } catch (error) {
    console.error('Error fetching active offers:', error);
    res.status(500).json({ message: 'Internal server error' });
  }
});

app.get('/api/offers/featured', (req, res) => {
  try {
    const featuredOffers = models.Offer.getFeatured();
    res.json(featuredOffers);
  } catch (error) {
    console.error('Error fetching featured offers:', error);
    res.status(500).json({ message: 'Internal server error' });
  }
});

app.get('/api/offers/:id', (req, res) => {
  try {
    const offer = models.Offer.getById(parseInt(req.params.id));

    if (!offer) {
      return res.status(404).json({ message: 'Offer not found' });
    }

    res.json(offer);
  } catch (error) {
    console.error('Error fetching offer:', error);
    res.status(500).json({ message: 'Internal server error' });
  }
});

app.get('/api/offers/slug/:slug', (req, res) => {
  try {
    const offer = models.Offer.getBySlug(req.params.slug);

    if (!offer) {
      return res.status(404).json({ message: 'Offer not found' });
    }

    res.json(offer);
  } catch (error) {
    console.error('Error fetching offer by slug:', error);
    res.status(500).json({ message: 'Internal server error' });
  }
});

app.get('/api/offers/:id/edit', authenticateToken, (req, res) => {
  try {
    const offer = models.Offer.getById(parseInt(req.params.id));

    if (!offer) {
      return res.status(404).json({ message: 'Offer not found' });
    }

    // Get discount options
    const discountOptions = [5, 10, 15, 20, 25, 30, 40, 50];

    // Return the offer with additional metadata for editing
    res.json({
      item: offer,
      metadata: {
        discountOptions,
        lastModified: new Date().toISOString(),
        canEdit: true,
        editableFields: [
          'title', 'slug', 'description', 'content', 'image_url',
          'discount', 'valid_from', 'valid_to', 'featured'
        ],
        validationRules: {
          valid_to: 'must be after valid_from',
          discount: 'must be between 1 and 100'
        }
      }
    });
  } catch (error) {
    console.error('Error fetching offer for edit:', error);
    res.status(500).json({ message: 'Internal server error' });
  }
});

app.post('/api/offers', authenticateToken, (req, res) => {
  try {
    const { title, slug, description, content, image_url, discount, valid_from, valid_to, featured } = req.body;

    // Check if slug already exists
    const existingOffer = models.Offer.getBySlug(slug);
    if (existingOffer) {
      return res.status(400).json({ message: 'Slug already exists' });
    }

    const newOffer = models.Offer.create({
      title,
      slug,
      description,
      content,
      image_url,
      discount: parseFloat(discount),
      valid_from,
      valid_to,
      featured: featured || false
    });

    // Emit real-time update
    emitUpdate('offer-created', newOffer);

    res.status(201).json(newOffer);
  } catch (error) {
    console.error('Error creating offer:', error);
    res.status(500).json({ message: 'Internal server error' });
  }
});

app.put('/api/offers/:id', authenticateToken, (req, res) => {
  try {
    const offerId = parseInt(req.params.id);
    const { title, slug, description, content, image_url, discount, valid_from, valid_to, featured } = req.body;

    // Check if slug already exists and it's not the current offer
    const existingOffer = models.Offer.getBySlug(slug);
    if (existingOffer && existingOffer.id !== offerId) {
      return res.status(400).json({ message: 'Slug already exists' });
    }

    const updatedOffer = models.Offer.update(offerId, {
      title,
      slug,
      description,
      content,
      image_url,
      discount: discount !== undefined ? parseFloat(discount) : undefined,
      valid_from,
      valid_to,
      featured
    });

    if (!updatedOffer) {
      return res.status(404).json({ message: 'Offer not found' });
    }

    // Emit real-time update
    emitUpdate('offer-updated', updatedOffer);

    res.json(updatedOffer);
  } catch (error) {
    console.error('Error updating offer:', error);
    res.status(500).json({ message: 'Internal server error' });
  }
});

app.delete('/api/offers/:id', authenticateToken, requireAdmin, (req, res) => {
  try {
    const offerId = parseInt(req.params.id);

    const deleted = models.Offer.deleteById(offerId);

    if (!deleted) {
      return res.status(404).json({ message: 'Offer not found' });
    }

    // Emit real-time update
    emitUpdate('offer-deleted', { id: offerId });

    res.json({ message: 'Offer deleted successfully' });
  } catch (error) {
    console.error('Error deleting offer:', error);
    res.status(500).json({ message: 'Internal server error' });
  }
});

// Inquiry routes
app.get('/api/inquiries', authenticateToken, (req, res) => {
  try {
    const inquiries = models.Inquiry.getAll();
    res.json(inquiries);
  } catch (error) {
    console.error('Error fetching inquiries:', error);
    res.status(500).json({ message: 'Internal server error' });
  }
});

app.get('/api/inquiries/unread', authenticateToken, (req, res) => {
  try {
    const unreadInquiries = models.Inquiry.getUnread();
    res.json(unreadInquiries);
  } catch (error) {
    console.error('Error fetching unread inquiries:', error);
    res.status(500).json({ message: 'Internal server error' });
  }
});

app.get('/api/inquiries/:id', authenticateToken, (req, res) => {
  try {
    const inquiry = models.Inquiry.getById(parseInt(req.params.id));

    if (!inquiry) {
      return res.status(404).json({ message: 'Inquiry not found' });
    }

    res.json(inquiry);
  } catch (error) {
    console.error('Error fetching inquiry:', error);
    res.status(500).json({ message: 'Internal server error' });
  }
});

app.get('/api/inquiries/:id/edit', authenticateToken, (req, res) => {
  try {
    const inquiry = models.Inquiry.getById(parseInt(req.params.id));

    if (!inquiry) {
      return res.status(404).json({ message: 'Inquiry not found' });
    }

    // Get status options
    const statusOptions = ['unread', 'read', 'replied'];

    // Return the inquiry with additional metadata for editing
    res.json({
      item: inquiry,
      metadata: {
        statusOptions,
        lastModified: new Date().toISOString(),
        canEdit: true,
        editableFields: ['status', 'reply'],
        canReply: inquiry.status !== 'replied',
        replyTemplate: `Dear ${inquiry.name},\n\nThank you for your inquiry about ${inquiry.subject}.\n\n[Your response here]\n\nBest regards,\nLux Voyage Team`
      }
    });
  } catch (error) {
    console.error('Error fetching inquiry for edit:', error);
    res.status(500).json({ message: 'Internal server error' });
  }
});

app.post('/api/inquiries', (req, res) => {
  try {
    const { name, email, phone, subject, message } = req.body;

    // Validate required fields
    if (!name || !email || !message) {
      return res.status(400).json({ message: 'Name, email, and message are required' });
    }

    const newInquiry = models.Inquiry.create({
      name,
      email,
      phone: phone || '',
      subject: subject || 'General Inquiry',
      message
    });

    // Emit real-time update
    emitUpdate('inquiry-created', newInquiry);

    res.status(201).json(newInquiry);
  } catch (error) {
    console.error('Error creating inquiry:', error);
    res.status(500).json({ message: 'Internal server error' });
  }
});

app.put('/api/inquiries/:id/read', authenticateToken, (req, res) => {
  try {
    const inquiryId = parseInt(req.params.id);

    const updatedInquiry = models.Inquiry.markAsRead(inquiryId);

    if (!updatedInquiry) {
      return res.status(404).json({ message: 'Inquiry not found' });
    }

    // Emit real-time update
    emitUpdate('inquiry-updated', updatedInquiry);

    res.json(updatedInquiry);
  } catch (error) {
    console.error('Error marking inquiry as read:', error);
    res.status(500).json({ message: 'Internal server error' });
  }
});

app.post('/api/inquiries/:id/reply', authenticateToken, (req, res) => {
  try {
    const inquiryId = parseInt(req.params.id);
    const { reply } = req.body;

    if (!reply) {
      return res.status(400).json({ message: 'Reply message is required' });
    }

    const updatedInquiry = models.Inquiry.reply(inquiryId, reply, req.user.id);

    if (!updatedInquiry) {
      return res.status(404).json({ message: 'Inquiry not found' });
    }

    // Emit real-time update
    emitUpdate('inquiry-replied', updatedInquiry);

    res.json(updatedInquiry);
  } catch (error) {
    console.error('Error replying to inquiry:', error);
    res.status(500).json({ message: 'Internal server error' });
  }
});

app.delete('/api/inquiries/:id', authenticateToken, requireAdmin, (req, res) => {
  try {
    const inquiryId = parseInt(req.params.id);

    const deleted = models.Inquiry.deleteById(inquiryId);

    if (!deleted) {
      return res.status(404).json({ message: 'Inquiry not found' });
    }

    // Emit real-time update
    emitUpdate('inquiry-deleted', { id: inquiryId });

    res.json({ message: 'Inquiry deleted successfully' });
  } catch (error) {
    console.error('Error deleting inquiry:', error);
    res.status(500).json({ message: 'Internal server error' });
  }
});

// Media routes
app.get('/api/media', (req, res) => {
  try {
    const media = models.Media.getAll();
    res.json(media);
  } catch (error) {
    console.error('Error fetching media:', error);
    res.status(500).json({ message: 'Internal server error' });
  }
});

app.get('/api/media/:id', (req, res) => {
  try {
    const media = models.Media.getById(parseInt(req.params.id));

    if (!media) {
      return res.status(404).json({ message: 'Media not found' });
    }

    res.json(media);
  } catch (error) {
    console.error('Error fetching media:', error);
    res.status(500).json({ message: 'Internal server error' });
  }
});

app.get('/api/media/:id/edit', authenticateToken, (req, res) => {
  try {
    const media = models.Media.getById(parseInt(req.params.id));

    if (!media) {
      return res.status(404).json({ message: 'Media not found' });
    }

    // Get media type options
    const typeOptions = ['image', 'video', 'document'];

    // Return the media with additional metadata for editing
    res.json({
      item: media,
      metadata: {
        typeOptions,
        lastModified: new Date().toISOString(),
        canEdit: true,
        editableFields: ['title', 'alt', 'description'],
        fileInfo: {
          url: media.url,
          type: media.type,
          size: media.size,
          dimensions: media.type === 'image' ? '1920x1080' : null
        }
      }
    });
  } catch (error) {
    console.error('Error fetching media for edit:', error);
    res.status(500).json({ message: 'Internal server error' });
  }
});

app.post('/api/media/upload', authenticateToken, (req, res, next) => {
  console.log('Media upload request received, authenticated user:', req.user.username);
  next();
}, upload.single('file'), (req, res) => {
  try {
    if (!req.file) {
      console.log('No file in upload request');
      return res.status(400).json({ message: 'No file uploaded' });
    }

    console.log('File uploaded successfully:', req.file.filename);
    const fileUrl = `/uploads/${req.file.filename}`;
    const fileType = req.file.mimetype.startsWith('image/') ? 'image' :
                    req.file.mimetype.startsWith('video/') ? 'video' : 'document';

    const newMedia = models.Media.create({
      title: req.body.title || req.file.originalname,
      alt: req.body.alt || req.file.originalname,
      description: req.body.description || '',
      url: fileUrl,
      type: fileType,
      size: `${(req.file.size / 1024).toFixed(2)} KB`,
      created_by: req.user.id
    });

    // Emit real-time update
    emitUpdate('media-uploaded', newMedia);

    console.log('Media item created:', newMedia.id);
    res.status(201).json(newMedia);
  } catch (error) {
    console.error('Error uploading media:', error);
    res.status(500).json({ message: 'Internal server error' });
  }
});

app.put('/api/media/:id', authenticateToken, (req, res) => {
  try {
    const mediaId = parseInt(req.params.id);

    const updatedMedia = models.Media.update(mediaId, req.body);

    if (!updatedMedia) {
      return res.status(404).json({ message: 'Media not found' });
    }

    // Emit real-time update
    emitUpdate('media-updated', updatedMedia);

    res.json(updatedMedia);
  } catch (error) {
    console.error('Error updating media:', error);
    res.status(500).json({ message: 'Internal server error' });
  }
});

app.delete('/api/media/:id', authenticateToken, requireAdmin, (req, res) => {
  try {
    const mediaId = parseInt(req.params.id);

    // Get media info before deletion to remove file
    const media = models.Media.getById(mediaId);

    if (!media) {
      return res.status(404).json({ message: 'Media not found' });
    }

    // Delete the file from the uploads directory
    const filePath = path.join(__dirname, media.url);
    if (fs.existsSync(filePath)) {
      fs.unlinkSync(filePath);
    }

    const deleted = models.Media.deleteById(mediaId);

    if (!deleted) {
      return res.status(404).json({ message: 'Media not found' });
    }

    // Emit real-time update
    emitUpdate('media-deleted', { id: mediaId });

    res.json({ message: 'Media deleted successfully' });
  } catch (error) {
    console.error('Error deleting media:', error);
    res.status(500).json({ message: 'Internal server error' });
  }
});

// Settings routes
app.get('/api/settings', (req, res) => {
  try {
    const settings = models.Settings.getAll();
    res.json(settings);
  } catch (error) {
    console.error('Error fetching settings:', error);
    res.status(500).json({ message: 'Internal server error' });
  }
});

app.get('/api/settings/:key', (req, res) => {
  try {
    const setting = models.Settings.getByKey(req.params.key);

    if (!setting) {
      return res.status(404).json({ message: 'Setting not found' });
    }

    res.json(setting);
  } catch (error) {
    console.error('Error fetching setting:', error);
    res.status(500).json({ message: 'Internal server error' });
  }
});

app.get('/api/settings/:key/edit', authenticateToken, requireAdmin, (req, res) => {
  try {
    const setting = models.Settings.getByKey(req.params.key);

    if (!setting) {
      return res.status(404).json({ message: 'Setting not found' });
    }

    // Determine value type and provide appropriate options
    let valueOptions = null;
    let valueType = 'text';

    if (setting.key_name === 'primaryColor' || setting.key_name === 'secondaryColor') {
      valueType = 'color';
    } else if (setting.key_name === 'maintenanceMode') {
      valueType = 'boolean';
      valueOptions = [
        { value: 'true', label: 'Enabled' },
        { value: 'false', label: 'Disabled' }
      ];
    } else if (setting.key_name === 'buttonStyle') {
      valueType = 'select';
      valueOptions = [
        { value: 'rounded', label: 'Rounded' },
        { value: 'square', label: 'Square' },
        { value: 'pill', label: 'Pill' }
      ];
    } else if (setting.key_name === 'fontFamily' || setting.key_name === 'headingFontFamily') {
      valueType = 'select';
      valueOptions = [
        { value: 'Inter', label: 'Inter' },
        { value: 'Roboto', label: 'Roboto' },
        { value: 'Open Sans', label: 'Open Sans' },
        { value: 'Cormorant Garamond', label: 'Cormorant Garamond' },
        { value: 'Playfair Display', label: 'Playfair Display' }
      ];
    }

    // Return the setting with additional metadata for editing
    res.json({
      item: setting,
      metadata: {
        valueType,
        valueOptions,
        lastModified: new Date().toISOString(),
        canEdit: true,
        group: setting.group_name,
        description: `Setting for ${setting.key_name} in the ${setting.group_name} group`
      }
    });
  } catch (error) {
    console.error('Error fetching setting for edit:', error);
    res.status(500).json({ message: 'Internal server error' });
  }
});

app.put('/api/settings/:key', authenticateToken, requireAdmin, (req, res) => {
  try {
    const { value } = req.body;

    const updated = models.Settings.updateByKey(req.params.key, value);

    if (!updated) {
      return res.status(404).json({ message: 'Setting not found' });
    }

    const setting = models.Settings.getByKey(req.params.key);

    // Emit real-time update
    emitUpdate('setting-updated', setting);

    res.json(setting);
  } catch (error) {
    console.error('Error updating setting:', error);
    res.status(500).json({ message: 'Internal server error' });
  }
});

// Error handling middleware
app.use((err, req, res, next) => {
  logger.error(err.stack);
  res.status(500).json({
    message: 'Something went wrong!',
    error: process.env.NODE_ENV === 'development' ? err.message : undefined
  });
});

// Initialize database and start server
async function startServer() {
  try {
    await initializeDatabase();
    console.log('Database initialized successfully');

    server.listen(PORT, () => {
      console.log(`Server running on port ${PORT}`);
      console.log('Database initialized with real-time capabilities');
    });
  } catch (error) {
    console.error('Failed to start server:', error);
    process.exit(1);
  }
}

startServer();
