#!/bin/bash
echo "Setting up and starting Lux Voyage Travel Agency Full Application..."
echo ""

# Install dependencies
echo "Installing dependencies..."
npm install

# Create uploads directory if it doesn't exist
if [ ! -d "uploads" ]; then
  echo "Creating uploads directory..."
  mkdir -p uploads
fi

# Create .env file if it doesn't exist
if [ ! -f ".env" ]; then
  echo "Creating .env file..."
  echo "REACT_APP_API_URL=http://localhost:5000/api" > .env
  echo "REACT_APP_SITE_NAME=Lux Voyage Travel Agency" >> .env
  echo "REACT_APP_UPLOAD_URL=http://localhost:5000/uploads" >> .env
fi

# Make start scripts executable
chmod +x start.sh
chmod +x start-full.sh

echo ""
echo "Setup complete!"
echo "Starting the application..."
echo ""

# Start the application in development mode
./start-full.sh
