#!/bin/bash
echo "Starting Lux Voyage Travel Agency with Fixed Backend..."
echo ""

# Kill any existing processes on port 5000
echo "Checking for existing processes on port 5000..."
lsof -ti:5000 | xargs kill -9 2>/dev/null

# Create uploads directory if it doesn't exist
if [ ! -d "uploads" ]; then
  echo "Creating uploads directory..."
  mkdir -p uploads
fi

# Create sample images directory if it doesn't exist
if [ ! -d "uploads/samples" ]; then
  echo "Creating sample images directory..."
  mkdir -p uploads/samples
fi

# Copy sample images if they don't exist
if [ ! -f "uploads/destination-luxury-resorts.jpg" ]; then
  echo "Creating sample images..."
  # Create empty sample files (in a real scenario, you would copy actual images)
  touch uploads/destination-luxury-resorts.jpg
  touch uploads/destination-private-islands.jpg
  touch uploads/destination-boutique-hotels.jpg
  touch uploads/experience-dolphin-cruise.jpg
  touch uploads/experience-cooking-class.jpg
  touch uploads/experience-underwater-photography.jpg
  touch uploads/offer-summer-special.jpg
  touch uploads/offer-honeymoon.jpg
  touch uploads/offer-last-minute.jpg
fi

# Start the backend server
echo "Starting backend server..."
node server.js &
BACKEND_PID=$!

# Wait for the backend to start
echo "Waiting for backend to start..."
sleep 3

# Check if backend is running
if ! curl -s http://localhost:5000/api/destinations > /dev/null; then
  echo "Error: Backend failed to start. Check server logs for details."
  kill $BACKEND_PID 2>/dev/null
  exit 1
fi

echo "Backend started successfully!"
echo ""
echo "Starting frontend..."
echo ""
echo "The application will be available at:"
echo "  - Frontend: http://localhost:3000"
echo "  - Admin Dashboard: http://localhost:3000/admin/login"
echo "    Username: admin"
echo "    Password: admin123"
echo "  - Backend API: http://localhost:5000/api"
echo ""
echo "Press Ctrl+C to stop both servers"
echo ""

# Start the frontend
npm start

# When npm start exits, kill the backend
kill $BACKEND_PID 2>/dev/null
