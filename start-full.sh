#!/bin/bash
echo "Starting Lux Voyage Travel Agency Full Application..."
echo "This will start both the React frontend and Express backend"
echo "The frontend will be available at http://localhost:3000"
echo "The backend API will be available at http://localhost:5000/api"
echo "Press Ctrl+C to stop both servers"
echo ""

# Install dependencies if needed
if [ ! -d "node_modules" ]; then
  echo "Installing dependencies..."
  npm install
fi

# Create uploads directory if it doesn't exist
if [ ! -d "uploads" ]; then
  echo "Creating uploads directory..."
  mkdir -p uploads
fi

# Start the application in development mode
npm run dev
