#!/bin/bash
echo "Starting Lux Voyage Travel Agency with Full CRUD Functionality..."
echo ""

# Check if node_modules exists
if [ ! -d "node_modules" ]; then
  echo "Installing dependencies..."
  npm install
fi

# Create uploads directory if it doesn't exist
if [ ! -d "uploads" ]; then
  echo "Creating uploads directory..."
  mkdir -p uploads
fi

# Create sample images directory if it doesn't exist
if [ ! -d "uploads/samples" ]; then
  echo "Creating sample images directory..."
  mkdir -p uploads/samples
fi

# Copy sample images if they don't exist
if [ ! -f "uploads/samples/destination-luxury-resorts.jpg" ]; then
  echo "Copying sample images..."
  # Create empty sample files (in a real scenario, you would copy actual images)
  touch uploads/samples/destination-luxury-resorts.jpg
  touch uploads/samples/destination-private-islands.jpg
  touch uploads/samples/destination-boutique-hotels.jpg
  touch uploads/samples/experience-dolphin-cruise.jpg
  touch uploads/samples/experience-cooking-class.jpg
  touch uploads/samples/experience-underwater-photography.jpg
  touch uploads/samples/offer-summer-special.jpg
  touch uploads/samples/offer-honeymoon.jpg
  touch uploads/samples/offer-last-minute.jpg
fi

# Create .env file if it doesn't exist
if [ ! -f ".env" ]; then
  echo "Creating .env file..."
  echo "REACT_APP_API_URL=http://localhost:5000/api" > .env
  echo "REACT_APP_SITE_NAME=Lux Voyage Travel Agency" >> .env
  echo "REACT_APP_UPLOAD_URL=http://localhost:5000/uploads" >> .env
fi

echo ""
echo "Starting the application with full CRUD functionality..."
echo "The frontend will be available at http://localhost:3000"
echo "The backend API will be available at http://localhost:5000/api"
echo "The admin dashboard will be available at http://localhost:3000/admin/login"
echo "  - Username: admin"
echo "  - Password: admin123"
echo ""
echo "Press Ctrl+C to stop both servers"
echo ""

# Start the application in development mode
npm run dev
