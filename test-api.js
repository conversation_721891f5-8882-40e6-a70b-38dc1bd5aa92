const axios = require('axios');

// Test the API endpoints
async function testApi() {
  try {
    // Test destinations endpoint
    const destinationsResponse = await axios.get('http://localhost:5000/api/destinations');
    console.log('✅ Destinations API test successful!');
    console.log(`Retrieved ${destinationsResponse.data.length} destinations`);
    
    // Test experiences endpoint
    const experiencesResponse = await axios.get('http://localhost:5000/api/experiences');
    console.log('✅ Experiences API test successful!');
    console.log(`Retrieved ${experiencesResponse.data.length} experiences`);
    
    // Test offers endpoint
    const offersResponse = await axios.get('http://localhost:5000/api/offers');
    console.log('✅ Offers API test successful!');
    console.log(`Retrieved ${offersResponse.data.length} offers`);
    
    console.log('\n🎉 All API tests passed successfully!');
  } catch (error) {
    console.error('❌ API test failed:', error.message);
    if (error.response) {
      console.error('Response status:', error.response.status);
      console.error('Response data:', error.response.data);
    }
  }
}

// Run the tests
testApi();
