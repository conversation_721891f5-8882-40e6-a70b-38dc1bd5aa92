#!/bin/bash
echo "Testing Backend API..."
echo ""

# Kill any existing processes on port 5000
echo "Checking for existing processes on port 5000..."
lsof -ti:5000 | xargs kill -9 2>/dev/null

# Start the backend server
echo "Starting backend server..."
node server.js &
BACKEND_PID=$!

# Wait for the backend to start
echo "Waiting for backend to start..."
sleep 3

# Test API endpoints
echo "Testing API endpoints..."
echo ""

echo "1. Testing /api/destinations endpoint:"
curl -s http://localhost:5000/api/destinations | head -c 300
echo "..."
echo ""

echo "2. Testing /api/experiences endpoint:"
curl -s http://localhost:5000/api/experiences | head -c 300
echo "..."
echo ""

echo "3. Testing /api/offers endpoint:"
curl -s http://localhost:5000/api/offers | head -c 300
echo "..."
echo ""

echo "4. Testing /api/settings endpoint:"
curl -s http://localhost:5000/api/settings | head -c 300
echo "..."
echo ""

echo ""
echo "Backend API is working correctly!"
echo "Press Ctrl+C to stop the server"
echo ""

# Wait for user to press Ctrl+C
wait $BACKEND_PID
