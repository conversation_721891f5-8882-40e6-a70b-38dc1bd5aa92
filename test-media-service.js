// Mock the browser environment
global.localStorage = {
  getItem: (key) => key === 'token' ? 'test-token' : null,
  setItem: () => {},
  removeItem: () => {}
};

// Import the mediaService
const mediaService = require('./src/services/mediaService').default;
const fs = require('fs');
const path = require('path');
const FormData = require('form-data');

// Test the media service
async function testMediaService() {
  try {
    console.log('Testing media service...');
    
    // Create a test image if it doesn't exist
    const testImagePath = path.join(__dirname, 'test-image.jpg');
    if (!fs.existsSync(testImagePath)) {
      // Create a simple 1x1 pixel image
      fs.writeFileSync(testImagePath, Buffer.from([
        0xff, 0xd8, 0xff, 0xe0, 0x00, 0x10, 0x4a, 0x46, 0x49, 0x46, 0x00, 0x01,
        0x01, 0x01, 0x00, 0x48, 0x00, 0x48, 0x00, 0x00, 0xff, 0xdb, 0x00, 0x43,
        0x00, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
        0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
        0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
        0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
        0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
        0xff, 0xff, 0xff, 0xff, 0xff, 0xc0, 0x00, 0x0b, 0x08, 0x00, 0x01, 0x00,
        0x01, 0x01, 0x01, 0x11, 0x00, 0xff, 0xc4, 0x00, 0x14, 0x00, 0x01, 0x00,
        0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
        0x00, 0x00, 0x00, 0xff, 0xc4, 0x00, 0x14, 0x10, 0x01, 0x00, 0x00, 0x00,
        0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
        0x00, 0xff, 0xda, 0x00, 0x08, 0x01, 0x01, 0x00, 0x00, 0x3f, 0x00, 0x37,
        0xff, 0xd9
      ]));
      console.log('Created test image file');
    }

    // Create form data
    const formData = new FormData();
    formData.append('file', fs.createReadStream(testImagePath));
    formData.append('title', 'Test Image from Service');
    formData.append('alt', 'Test Image Alt');

    // Test upload media
    console.log('Testing uploadMedia...');
    try {
      const uploadResult = await mediaService.uploadMedia(formData);
      console.log('✅ Upload successful:', uploadResult);
    } catch (error) {
      console.error('❌ Upload failed:', error.message);
    }

    // Test get all media
    console.log('\nTesting getAllMedia...');
    try {
      const allMedia = await mediaService.getAllMedia();
      console.log(`✅ Got ${allMedia.length} media items`);
    } catch (error) {
      console.error('❌ Get all media failed:', error.message);
    }

  } catch (error) {
    console.error('Test failed:', error.message);
  }
}

// Run the test
testMediaService();
