const axios = require('axios');

const API_BASE = 'http://localhost:5000/api';

// Test credentials
const testUser = {
  username: 'admin',
  password: 'admin123'
};

let authToken = '';

// Login and get token
async function login() {
  try {
    const response = await axios.post(`${API_BASE}/auth/login`, testUser);
    authToken = response.data.token;
    console.log('✅ Login successful');
    console.log('User:', response.data.user.name, '(' + response.data.user.role + ')');
    return true;
  } catch (error) {
    console.error('❌ Login failed:', error.response?.data?.message || error.message);
    return false;
  }
}

// Test database operations
async function testDatabaseOperations() {
  const headers = {
    'Authorization': `Bearer ${authToken}`,
    'Content-Type': 'application/json'
  };

  console.log('\n🔄 Testing Real-time Database Operations...\n');

  try {
    // Test 1: Get all destinations
    console.log('1. Fetching destinations...');
    const destinations = await axios.get(`${API_BASE}/destinations`);
    console.log(`   Found ${destinations.data.length} destinations`);

    // Test 2: Get all experiences
    console.log('2. Fetching experiences...');
    const experiences = await axios.get(`${API_BASE}/experiences`);
    console.log(`   Found ${experiences.data.length} experiences`);

    // Test 3: Get all offers
    console.log('3. Fetching offers...');
    const offers = await axios.get(`${API_BASE}/offers`);
    console.log(`   Found ${offers.data.length} offers`);

    // Test 4: Get all users (admin only)
    console.log('4. Fetching users...');
    const users = await axios.get(`${API_BASE}/users`, { headers });
    console.log(`   Found ${users.data.length} users`);

    // Test 5: Get all inquiries (admin only)
    console.log('5. Fetching inquiries...');
    const inquiries = await axios.get(`${API_BASE}/inquiries`, { headers });
    console.log(`   Found ${inquiries.data.length} inquiries`);

    // Test 6: Get all media
    console.log('6. Fetching media...');
    const media = await axios.get(`${API_BASE}/media`);
    console.log(`   Found ${media.data.length} media items`);

    // Test 7: Get all settings
    console.log('7. Fetching settings...');
    const settings = await axios.get(`${API_BASE}/settings`);
    console.log(`   Found ${settings.data.length} settings`);

    // Test 8: Create a new destination (this will trigger real-time update)
    console.log('\n8. Creating new destination (will trigger real-time notification)...');
    const newDestination = {
      title: 'Test Resort',
      slug: 'test-resort-' + Date.now(),
      description: 'A test resort for real-time database testing',
      content: '<p>This is a test destination created to demonstrate real-time database functionality.</p>',
      image_url: '/uploads/test-resort.jpg',
      category: 'resorts',
      featured: false
    };

    const createdDestination = await axios.post(`${API_BASE}/destinations`, newDestination, { headers });
    console.log(`   ✅ Created destination: ${createdDestination.data.title} (ID: ${createdDestination.data.id})`);

    // Test 9: Update the destination (this will trigger real-time update)
    console.log('9. Updating destination (will trigger real-time notification)...');
    const updatedData = {
      ...newDestination,
      title: 'Updated Test Resort',
      featured: true
    };

    const updatedDestination = await axios.put(`${API_BASE}/destinations/${createdDestination.data.id}`, updatedData, { headers });
    console.log(`   ✅ Updated destination: ${updatedDestination.data.title}`);

    // Test 10: Create a new inquiry (this will trigger real-time update)
    console.log('10. Creating new inquiry (will trigger real-time notification)...');
    const newInquiry = {
      name: 'Test User',
      email: '<EMAIL>',
      phone: '+1234567890',
      subject: 'Real-time Database Test',
      message: 'This is a test inquiry to demonstrate real-time database functionality.'
    };

    const createdInquiry = await axios.post(`${API_BASE}/inquiries`, newInquiry);
    console.log(`   ✅ Created inquiry: ${createdInquiry.data.subject} (ID: ${createdInquiry.data.id})`);

    // Test 11: Reply to the inquiry (this will trigger real-time update)
    console.log('11. Replying to inquiry (will trigger real-time notification)...');
    const replyData = {
      reply: 'Thank you for your inquiry! This is an automated test reply to demonstrate real-time functionality.'
    };

    const repliedInquiry = await axios.post(`${API_BASE}/inquiries/${createdInquiry.data.id}/reply`, replyData, { headers });
    console.log(`   ✅ Replied to inquiry: ${repliedInquiry.data.name}`);

    // Test 12: Clean up - delete the test destination
    console.log('12. Cleaning up - deleting test destination...');
    await axios.delete(`${API_BASE}/destinations/${createdDestination.data.id}`, { headers });
    console.log(`   ✅ Deleted test destination`);

    console.log('\n🎉 All database operations completed successfully!');
    console.log('\n📱 Check your admin dashboard for real-time notifications!');
    console.log('   - Login at: http://localhost:3000/admin/login');
    console.log('   - Username: admin');
    console.log('   - Password: admin123');

  } catch (error) {
    console.error('❌ Database operation failed:', error.response?.data?.message || error.message);
  }
}

// Main test function
async function runTests() {
  console.log('🚀 Starting Real-time Database Tests...\n');
  
  const loginSuccess = await login();
  if (!loginSuccess) {
    console.log('❌ Cannot proceed without authentication');
    return;
  }

  await testDatabaseOperations();
  
  console.log('\n✨ Real-time database testing completed!');
  console.log('\n💡 Features demonstrated:');
  console.log('   - SQLite database with better-sqlite3');
  console.log('   - Real-time Socket.IO notifications');
  console.log('   - Complete CRUD operations');
  console.log('   - Authentication and authorization');
  console.log('   - Structured data models');
  console.log('   - Admin dashboard integration');
}

// Run the tests
runTests().catch(console.error);
