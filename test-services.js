// Mock the localStorage for Node.js environment
global.localStorage = {
  getItem: () => null,
  setItem: () => {},
  removeItem: () => {}
};

// Mock window.location
global.window = {
  location: {
    href: ''
  }
};

// Set environment variable for API URL
process.env.REACT_APP_API_URL = 'http://localhost:5000/api';

// Import axios
const axios = require('axios');

// Create a simple version of the api service
const api = axios.create({
  baseURL: process.env.REACT_APP_API_URL,
  headers: {
    'Content-Type': 'application/json',
  },
});

// Add interceptors similar to the ones in the actual service
api.interceptors.request.use(
  (config) => {
    const token = localStorage.getItem('token');
    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
    }
    return config;
  },
  (error) => Promise.reject(error)
);

// Test the service endpoints
async function testServices() {
  try {
    console.log('Testing service endpoints with baseURL:', process.env.REACT_APP_API_URL);
    
    // Test destinations endpoint (similar to what destinationService would do)
    const destinationsResponse = await api.get('/destinations');
    console.log('✅ Destinations service test successful!');
    console.log(`Retrieved ${destinationsResponse.data.length} destinations`);
    
    // Test experiences endpoint (similar to what experienceService would do)
    const experiencesResponse = await api.get('/experiences');
    console.log('✅ Experiences service test successful!');
    console.log(`Retrieved ${experiencesResponse.data.length} experiences`);
    
    // Test offers endpoint (similar to what offerService would do)
    const offersResponse = await api.get('/offers');
    console.log('✅ Offers service test successful!');
    console.log(`Retrieved ${offersResponse.data.length} offers`);
    
    console.log('\n🎉 All service tests passed successfully!');
  } catch (error) {
    console.error('❌ Service test failed:', error.message);
    if (error.response) {
      console.error('Response status:', error.response.status);
      console.error('Response data:', error.response.data);
    }
  }
}

// Run the tests
testServices();
